# React Native/Expo Codebase Refactoring Guide

This document outlines the comprehensive refactoring performed on the React Native/Expo application to improve code organization, reusability, and maintainability while following best practices.

## 🎯 Refactoring Objectives

1. **Component Extraction and Reusability**: Extract duplicate UI patterns into reusable components
2. **Utility Functions Organization**: Consolidate utility functions into focused modules
3. **Performance Optimization**: Implement React.memo(), optimize FlatLists, and add error boundaries
4. **Code Organization**: Organize files by feature with consistent directory structure

## 📁 New Directory Structure

```
app/
├── components/
│   ├── shared/           # 🆕 Shared reusable components
│   │   ├── cards/        # Card components (BaseCard, StatCard)
│   │   ├── layout/       # Layout components (StatsGrid, SectionHeader, ListContainer)
│   │   ├── states/       # State components (LoadingState, ErrorState, EmptyState)
│   │   ├── stats/        # Stats components (StatsRow, StatColumn)
│   │   ├── lists/        # Optimized list components
│   │   ├── match/        # Match-specific components
│   │   ├── error/        # Error boundary components
│   │   └── index.ts      # Centralized exports
│   ├── heroes/           # Hero-specific components
│   ├── matches/          # Match-specific components
│   └── ui/               # Base UI components
├── utils/
│   ├── format/           # 🆕 Formatting utilities
│   │   ├── dateFormat.ts
│   │   ├── numberFormat.ts
│   │   ├── durationFormat.ts
│   │   └── gameFormat.ts
│   ├── validation/       # 🆕 Validation utilities
│   │   ├── typeGuards.ts
│   │   ├── gameValidation.ts
│   │   └── dataValidation.ts
│   ├── api/              # 🆕 API utilities
│   │   ├── errorHandling.ts
│   │   ├── queryHelpers.ts
│   │   └── responseUtils.ts
│   └── ...               # Existing utilities
└── hooks/
    ├── useOptimizedState.tsx  # 🆕 Performance-optimized hooks
    └── ...                    # Existing hooks
```

## 🧩 Shared Components Library

### Cards
- **BaseCard**: Foundation card component with consistent styling
- **StatCard**: Enhanced stat display with icons and better typography

### Layout
- **StatsGrid**: Flexible grid layout for stats display
- **SectionHeader**: Consistent section headers with optional actions
- **ListContainer**: Wrapper for lists with card styling options

### States
- **LoadingState**: Consistent loading indicators with messages
- **ErrorState**: Error display with retry functionality
- **EmptyState**: Empty state with optional actions

### Performance Components
- **OptimizedFlatList**: FlatList with built-in performance optimizations
- **OptimizedMatchItem**: Memoized match item with better performance
- **ErrorBoundary**: Error boundary with fallback UI

## 🛠 Utility Functions

### Format Utilities (`app/utils/format/`)
```typescript
// Date formatting
import { formatRelativeTime, formatAbsoluteDate } from "@/utils/format";

// Number formatting
import { formatNumber, formatPercentage, formatNetWorth } from "@/utils/format";

// Duration formatting
import { formatMatchDuration, formatTimePlayed } from "@/utils/format";

// Game-specific formatting
import { parseMatchMode, formatWinRate } from "@/utils/format";
```

### Validation Utilities (`app/utils/validation/`)
```typescript
// Type guards
import { isValidNumber, isValidString, hasValidStringProp } from "@/utils/validation";

// Game validation
import { isValidSteamIdFormat, isMatchWon, isValidBadgeNumber } from "@/utils/validation";

// Data validation
import { validateArrayItems, isValidPagination } from "@/utils/validation";
```

### API Utilities (`app/utils/api/`)
```typescript
// Error handling
import { handleApiResponse, createApiError, retryApiCall } from "@/utils/api";

// Query helpers
import { createQueryOptions, queryKeys, DEFAULT_QUERY_OPTIONS } from "@/utils/api";

// Response utilities
import { transformResponse, filterResponseData, paginateResponseData } from "@/utils/api";
```

## 🚀 Performance Optimizations

### React.memo() Implementation
All new shared components use React.memo() to prevent unnecessary re-renders:

```typescript
export const StatCard = React.memo<StatCardProps>(function StatCard(props) {
  // Component implementation
});
```

### Optimized FlatList
The `OptimizedFlatList` component includes:
- `removeClippedSubviews: true`
- Configurable `maxToRenderPerBatch`, `windowSize`, `initialNumToRender`
- Optional `getItemLayout` for consistent item sizes
- Built-in loading, error, and empty states

### Memoized Callbacks
Use `useCallback` for event handlers to prevent child re-renders:

```typescript
const handlePress = useCallback((item: Item) => {
  onPress(item);
}, [onPress]);
```

### Optimized State Management
New hooks for better state management:

```typescript
// Prevents unnecessary re-renders with shallow comparison
const [state, setState] = useOptimizedState(initialState);

// Manages loading states with automatic cleanup
const { isLoading, startLoading, stopLoading } = useLoadingState();

// Handles async operations with built-in error handling
const { data, isLoading, error, execute } = useAsyncOperation();
```

## 📝 Usage Examples

### Using Shared Components
```typescript
import { 
  StatCard, 
  StatsGrid, 
  SectionHeader, 
  OptimizedFlatList,
  ErrorBoundary 
} from "@/components/shared";

// Stats display
<StatsGrid columns={2} gap="sm">
  <StatCard title="Matches" value={42} />
  <StatCard title="Win Rate" value="65%" valueColor="#22c55e" />
</StatsGrid>

// Optimized list
<OptimizedFlatList
  data={items}
  renderItem={renderItem}
  keyExtractor={keyExtractor}
  performance={{
    estimatedItemSize: 120,
    maxToRenderPerBatch: 10,
  }}
  emptyState={{
    title: "No items",
    message: "No items to display",
  }}
/>
```

### Using Format Utilities
```typescript
import { formatRelativeTime, formatNumber, formatMatchDuration } from "@/utils/format";

const formattedTime = formatRelativeTime(timestamp);
const formattedNumber = formatNumber(1234567); // "1.2M"
const formattedDuration = formatMatchDuration(1830); // "30:30"
```

### Using Validation Utilities
```typescript
import { isValidNumber, isMatchWon } from "@/utils/validation";

if (isValidNumber(value) && value > 0) {
  // Handle valid positive number
}

const won = isMatchWon(match);
```

## 🔄 Migration Guide

### Replacing Old Components
1. **StatCard**: Replace `@/components/profile/StatCard` with `@/components/shared/StatCard`
2. **MatchItem**: Replace with `OptimizedMatchItem` for better performance
3. **Loading/Error States**: Use shared `LoadingState`, `ErrorState`, `EmptyState`

### Updating Utility Imports
```typescript
// Old
import { formatRelativeTime } from "@/utils/matchHistoryStats";

// New
import { formatRelativeTime } from "@/utils/format";
```

### Adding Error Boundaries
Wrap screens or components with error boundaries:

```typescript
<ErrorBoundary
  errorTitle="Screen Error"
  errorMessage="Something went wrong"
  showRetry
>
  <YourComponent />
</ErrorBoundary>
```

## 🎨 Styling Consistency

All shared components follow the established theming patterns:
- Use `useAppTheme()` hook for theme access
- Implement `ThemedStyle` for consistent styling
- Support both light and dark themes
- Follow spacing and typography guidelines

## 🧪 Testing Considerations

- All shared components are designed to be easily testable
- Utility functions are pure functions where possible
- Error boundaries provide fallback UI for testing error scenarios
- Mocked API responses can be created using `createMockResponse`

## 📈 Benefits

1. **Reduced Code Duplication**: Shared components eliminate duplicate UI patterns
2. **Improved Performance**: React.memo(), optimized FlatLists, and better state management
3. **Better Error Handling**: Consistent error boundaries and error states
4. **Enhanced Maintainability**: Organized utility functions and clear separation of concerns
5. **Type Safety**: Comprehensive TypeScript interfaces and type guards
6. **Consistent UX**: Standardized loading, error, and empty states

## 🔮 Future Improvements

1. **Automated Testing**: Add unit tests for shared components and utilities
2. **Storybook Integration**: Create stories for shared components
3. **Performance Monitoring**: Add performance metrics and monitoring
4. **Accessibility**: Enhance accessibility features across components
5. **Internationalization**: Expand i18n support in shared components
