/**
 * API error handling utilities
 */

import type { ApiResponse } from "apisauce";

/**
 * Standard API error types
 */
export enum ApiErrorType {
  NETWORK_ERROR = "NETWORK_ERROR",
  TIMEOUT_ERROR = "TIMEOUT_ERROR",
  SERVER_ERROR = "SERVER_ERROR",
  CLIENT_ERROR = "CLIENT_ERROR",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",
}

/**
 * Standardized API error interface
 */
export interface ApiError {
  type: ApiErrorType;
  message: string;
  statusCode?: number;
  originalError?: unknown;
}

/**
 * Creates a standardized API error from an API response
 * @param response The failed API response
 * @param customMessage Optional custom error message
 * @returns Standardized API error
 */
export function createApiError(response: ApiResponse<any>, customMessage?: string): ApiError {
  const statusCode = response.status;
  let type: ApiErrorType;
  let message: string;

  // Determine error type based on status code
  if (!statusCode) {
    type = ApiErrorType.NETWORK_ERROR;
    message = "Network connection failed";
  } else if (statusCode >= 500) {
    type = ApiErrorType.SERVER_ERROR;
    message = "Server error occurred";
  } else if (statusCode >= 400) {
    type = ApiErrorType.CLIENT_ERROR;
    message = "Client error occurred";
  } else {
    type = ApiErrorType.UNKNOWN_ERROR;
    message = "Unknown error occurred";
  }

  // Use custom message if provided
  if (customMessage) {
    message = customMessage;
  } else if (response.data?.message) {
    message = response.data.message;
  }

  return {
    type,
    message,
    statusCode,
    originalError: response.originalError,
  };
}

/**
 * Handles API response and throws standardized error if failed
 * @param response The API response to handle
 * @param errorMessage Optional custom error message
 * @returns The response data if successful
 * @throws ApiError if the response failed
 */
export function handleApiResponse<T>(response: ApiResponse<T>, errorMessage?: string): T {
  if (!response.ok) {
    throw createApiError(response, errorMessage);
  }

  if (!response.data) {
    throw createApiError(response, "No data received from server");
  }

  return response.data;
}

/**
 * Safely handles API response without throwing
 * @param response The API response to handle
 * @returns Object with data or error
 */
export function safeHandleApiResponse<T>(response: ApiResponse<T>): {
  data?: T;
  error?: ApiError;
} {
  if (!response.ok) {
    return { error: createApiError(response) };
  }

  if (!response.data) {
    return { error: createApiError(response, "No data received from server") };
  }

  return { data: response.data };
}

/**
 * Retries an API call with exponential backoff
 * @param apiCall Function that returns a Promise of API response
 * @param maxRetries Maximum number of retries
 * @param baseDelay Base delay in milliseconds
 * @returns Promise of the API response
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
): Promise<ApiResponse<T>> {
  let lastError: ApiError | undefined;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await apiCall();

      // If successful or client error (don't retry client errors), return
      if (response.ok || (response.status && response.status < 500)) {
        return response;
      }

      lastError = createApiError(response);
    } catch (error) {
      lastError = {
        type: ApiErrorType.UNKNOWN_ERROR,
        message: "Unexpected error during API call",
        originalError: error,
      };
    }

    // Don't delay after the last attempt
    if (attempt < maxRetries) {
      const delay = baseDelay * 2 ** attempt;
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // If we get here, all retries failed
  throw lastError;
}

/**
 * Creates a user-friendly error message from an API error
 * @param error The API error
 * @returns User-friendly error message
 */
export function getErrorMessage(error: ApiError): string {
  switch (error.type) {
    case ApiErrorType.NETWORK_ERROR:
      return "Please check your internet connection and try again.";
    case ApiErrorType.TIMEOUT_ERROR:
      return "The request timed out. Please try again.";
    case ApiErrorType.SERVER_ERROR:
      return "Server is temporarily unavailable. Please try again later.";
    case ApiErrorType.CLIENT_ERROR:
      if (error.statusCode === 404) {
        return "The requested resource was not found.";
      }
      if (error.statusCode === 401) {
        return "You are not authorized to access this resource.";
      }
      return error.message || "Invalid request. Please check your input.";
    case ApiErrorType.VALIDATION_ERROR:
      return error.message || "Please check your input and try again.";
    default:
      return error.message || "An unexpected error occurred. Please try again.";
  }
}

/**
 * Checks if an error is retryable
 * @param error The API error to check
 * @returns true if the error is retryable
 */
export function isRetryableError(error: ApiError): boolean {
  return [ApiErrorType.NETWORK_ERROR, ApiErrorType.TIMEOUT_ERROR, ApiErrorType.SERVER_ERROR].includes(error.type);
}
