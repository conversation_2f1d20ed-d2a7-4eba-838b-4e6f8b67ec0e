import React from "react";
import { View, type ViewStyle } from "react-native";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";
import { BaseCard } from "../cards/BaseCard";

export interface ListContainerProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * Children components to render inside the container
   */
  children: React.ReactNode;
  /**
   * Whether to use card styling
   */
  variant?: "card" | "plain";
  /**
   * Whether to add padding around the content
   */
  padded?: boolean;
}

/**
 * A container component for lists that provides consistent styling
 * Can be used with or without card styling
 */
export const ListContainer = React.memo<ListContainerProps>(function ListContainer(props) {
  const { style, children, variant = "card", padded = true } = props;
  const { themed } = useAppTheme();

  const containerStyle = [padded && themed($padding), style];

  if (variant === "card") {
    return (
      <BaseCard style={containerStyle} variant="elevated">
        {children}
      </BaseCard>
    );
  }

  return <View style={containerStyle}>{children}</View>;
});

const $padding: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.sm,
});
