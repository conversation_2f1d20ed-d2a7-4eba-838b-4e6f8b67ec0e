/**
 * Number formatting utilities
 */

/**
 * Formats a number with appropriate suffixes (K, M, B)
 * @param num The number to format
 * @param decimals Number of decimal places (default: 1)
 * @returns Formatted number string
 */
export function formatNumber(num: number, decimals: number = 1): string {
  if (num === 0) return "0";

  const absNum = Math.abs(num);
  const sign = num < 0 ? "-" : "";

  if (absNum >= 1_000_000_000) {
    return `${sign}${(absNum / 1_000_000_000).toFixed(decimals)}B`;
  }
  if (absNum >= 1_000_000) {
    return `${sign}${(absNum / 1_000_000).toFixed(decimals)}M`;
  }
  if (absNum >= 1_000) {
    return `${sign}${(absNum / 1_000).toFixed(decimals)}K`;
  }

  return num.toString();
}

/**
 * Formats a number as currency
 * @param amount The amount to format
 * @param currency Currency code (default: 'USD')
 * @param locale Locale for formatting (default: 'en-US')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = "USD", locale: string = "en-US"): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
  }).format(amount);
}

/**
 * Formats a number as a percentage
 * @param value The value to format (0.5 = 50%)
 * @param decimals Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * Formats a number with thousands separators
 * @param num The number to format
 * @param locale Locale for formatting (default: 'en-US')
 * @returns Formatted number string with separators
 */
export function formatWithSeparators(num: number, locale: string = "en-US"): string {
  return new Intl.NumberFormat(locale).format(num);
}

/**
 * Formats net worth in a compact format (e.g., "25.5k", "1.2M")
 * @param netWorth The net worth value
 * @returns Formatted net worth string
 */
export function formatNetWorth(netWorth: number): string {
  if (netWorth >= 1_000_000) {
    return `${(netWorth / 1_000_000).toFixed(1)}M`;
  }
  if (netWorth >= 1_000) {
    return `${(netWorth / 1_000).toFixed(0)}k`;
  }
  return netWorth.toString();
}

/**
 * Formats a ratio with proper decimal places
 * @param numerator The numerator value
 * @param denominator The denominator value
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted ratio string
 */
export function formatRatio(numerator: number, denominator: number, decimals: number = 2): string {
  if (denominator === 0) {
    return numerator > 0 ? "∞" : "0";
  }
  return (numerator / denominator).toFixed(decimals);
}

/**
 * Formats a KDA ratio
 * @param kills Number of kills
 * @param deaths Number of deaths
 * @param assists Number of assists
 * @returns Formatted KDA ratio string
 */
export function formatKDA(kills: number, deaths: number, assists: number): string {
  const ratio = deaths === 0 ? kills + assists : (kills + assists) / deaths;
  return ratio.toFixed(2);
}
