/**
 * Type guard utilities for runtime type checking
 */

/**
 * Type-safe utility to check if an unknown object has a valid string property.
 * This is particularly useful in React 19 where props are typed as unknown by default.
 * The function safely narrows down the type by checking both property existence and type.
 */
export function hasValidStringProp(props: unknown, propName: string): boolean {
  return (
    props !== null &&
    typeof props === "object" &&
    propName in props &&
    typeof (props as Record<string, unknown>)[propName] === "string"
  );
}

/**
 * Type guard to check if a value is a valid number
 */
export function isValidNumber(value: unknown): value is number {
  return typeof value === "number" && !Number.isNaN(value) && Number.isFinite(value);
}

/**
 * Type guard to check if a value is a valid positive number
 */
export function isPositiveNumber(value: unknown): value is number {
  return isValidNumber(value) && value > 0;
}

/**
 * Type guard to check if a value is a valid non-negative number
 */
export function isNonNegativeNumber(value: unknown): value is number {
  return isValidNumber(value) && value >= 0;
}

/**
 * Type guard to check if a value is a valid string
 */
export function isValidString(value: unknown): value is string {
  return typeof value === "string" && value.length > 0;
}

/**
 * Type guard to check if a value is a valid non-empty array
 */
export function isNonEmptyArray<T>(value: unknown): value is T[] {
  return Array.isArray(value) && value.length > 0;
}

/**
 * Type guard to check if a value is a valid object (not null, not array)
 */
export function isValidObject(value: unknown): value is Record<string, unknown> {
  return typeof value === "object" && value !== null && !Array.isArray(value);
}

/**
 * Type guard to check if a value is a valid Unix timestamp
 */
export function isValidTimestamp(value: unknown): value is number {
  if (!isValidNumber(value)) return false;

  // Check if it's a reasonable timestamp (after 1970 and before year 3000)
  const minTimestamp = 0;
  const maxTimestamp = 32503680000; // Year 3000

  return value >= minTimestamp && value <= maxTimestamp;
}

/**
 * Type guard to check if a value is a valid Steam ID
 */
export function isValidSteamId(value: unknown): value is string {
  if (!isValidString(value)) return false;

  // Steam64 ID should be a 17-digit number
  const steamIdRegex = /^\d{17}$/;
  return steamIdRegex.test(value);
}

/**
 * Type guard to check if a value is a valid email address
 */
export function isValidEmail(value: unknown): value is string {
  if (!isValidString(value)) return false;

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Type guard to check if a value is a valid URL
 */
export function isValidUrl(value: unknown): value is string {
  if (!isValidString(value)) return false;

  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Type guard to check if a value is a valid hex color
 */
export function isValidHexColor(value: unknown): value is string {
  if (!isValidString(value)) return false;

  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(value);
}
