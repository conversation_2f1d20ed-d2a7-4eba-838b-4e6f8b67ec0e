{"common": {"ok": "OK!", "cancel": "Cancel", "back": "Back", "logOut": "Log Out", "noSteamAccount": "No Account", "linkSteamAccount": "Link your Steam account to view stats", "steamNotLinked": "Steam account not linked", "victory": "Victory", "defeat": "Defeat", "loading": "Loading..."}, "errorScreen": {"title": "Something went wrong!", "friendlySubtitle": "Something went wrong.", "reset": "RESET APP", "traceTitle": "Error from %{name} stack"}, "emptyStateComponent": {"generic": {"heading": "So empty... so sad", "content": "No data found yet. Try clicking the button to refresh or reload the app.", "button": "Let's try this again"}}, "mainNavigator": {"dashboardTab": "Dashboard", "matchesTab": "Matches", "heroesTab": "Heroes", "profileTab": "Profile", "settingsTab": "Settings"}, "welcomeScreen": {"title": "Welcome to DeadlockStats", "subtitle": "Track your Deadlock game statistics and performance", "description": "Sign in with your Steam account to get started and view your detailed game analytics.", "signInButton": "Sign in with Steam", "signInButtonLoading": "Signing in...", "skipButton": "Skip for now", "skipDescription": "You can link your Steam account later in settings", "errorTitle": "Authentication Error", "errorMessage": "Failed to authenticate with <PERSON>. Please try again.", "errorRetryButton": "Try Again", "loadingMessage": "Authenticating with Steam..."}, "dashboardScreen": {"recentMatches": "Recent Matches", "viewAllMatches": "View All Matches", "loadingMatchHistory": "Loading match history...", "failedToLoadMatchHistory": "Failed to load match history", "noMatchesFound": "No matches found", "noSteamAccountLinked": "Link your Steam account to view your match history and statistics", "winRate7Days": "Win Rate (7d)", "avgKda7Days": "Avg <PERSON> (7d)", "bestMate30d": "Best Mate (30d)", "worstEnemy30d": "Worst Enemy (30d)", "mainHeroOverall": "Main Hero (All)", "bestHeroOverall": "Best Hero (All)"}, "playerSearchScreen": {"title": "Search Player", "searchPlaceholder": "Enter steam name...", "searchResults": "Search Results", "noPlayersFound": "No players found", "tryCheckingSpelling": "Try checking the spelling", "recentSearches": "Recent Searches"}, "profileSharing": {"shareProfile": "Share Profile", "shareMessage": "Check out this Deadlock player profile:", "shareError": "Failed to share profile", "loadingSharedProfile": "Loading shared profile...", "sharedProfileError": "Failed to load shared profile", "invalidProfileLink": "Invalid profile link"}, "heroesStatsScreen": {"loadingHeroStats": "Loading hero stats...", "noHeroStatsFound": "No hero stats found", "noSteamAccountLinked": "Link your Steam account to view your hero statistics", "viewHeroMatches": "View Matches"}, "heroDetailsScreen": {"heroMatches": "Hero Matches", "viewHeroMatches": "View Matches"}, "matchesListScreen": {"loadingMatchHistory": "Loading match history...", "noMatchesFound": "No matches found", "noSteamAccountLinked": "Link your Steam account to view your match history"}, "timeRangeSelect": {"label24h": "24h", "label7d": "7d", "label30d": "30d", "labelAll": "All"}, "matchDetailsScreen": {"title": "Match", "loadingMessage": "Loading match details...", "errorTitle": "Error", "errorMessage": "Failed to load match details. Please try again.", "duration": "Duration", "teamAmber": "Team Amber", "teamSapphire": "Team Sapphire", "teamAverage": "Team Average", "unranked": "Unranked", "playerNameLabel": "Name", "playerKdaLabel": "KDA", "playerSoulsLabel": "Souls", "playerDamageLabel": "Damage", "playerSoulsLabelShort": "NET", "playerDamageLabelShort": "DMG", "kills": "K", "deaths": "D", "assists": "A", "netWorth": "Net Worth", "level": "Lvl", "denies": "D", "lastHits": "LH", "loadingPlayer": "Loading...", "player": "Player", "playerStats": "Player Stats", "viewProfile": "View Profile", "hero": "Hero"}, "settingsScreen": {"title": "Settings", "appearanceSection": "Appearance", "darkMode": "Dark Mode", "useDarkTheme": "Use dark theme", "supportSection": "Support", "contactUs": "Contact us", "joinDiscordServer": "Join our Discord server", "privacyPolicy": "Privacy Policy", "readPrivacyPolicy": "Read our privacy policy", "accountSection": "Account", "signOut": "Sign Out", "logOutOfAccount": "Log out of your account", "linkToSteam": "Link to Steam", "linkSteamAccount": "Link your Steam account to view stats", "version": "Version"}}