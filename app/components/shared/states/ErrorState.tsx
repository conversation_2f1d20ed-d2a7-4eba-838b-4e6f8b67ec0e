import React from "react";
import { View, type ViewStyle } from "react-native";
import { Button, type ButtonProps } from "@/components/ui/Button";
import { Text, type TextProps } from "@/components/ui/Text";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface ErrorStateProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * Error title
   */
  title?: string;
  /**
   * Error title which is looked up via i18n.
   */
  titleTx?: TextProps["tx"];
  /**
   * Optional title options to pass to i18n.
   */
  titleTxOptions?: TextProps["txOptions"];
  /**
   * Error message
   */
  message?: string;
  /**
   * Error message which is looked up via i18n.
   */
  messageTx?: TextProps["tx"];
  /**
   * Optional message options to pass to i18n.
   */
  messageTxOptions?: TextProps["txOptions"];
  /**
   * Optional retry button text
   */
  retryText?: string;
  /**
   * Retry button text which is looked up via i18n.
   */
  retryTx?: TextProps["tx"];
  /**
   * Optional retry text options to pass to i18n.
   */
  retryTxOptions?: TextProps["txOptions"];
  /**
   * Callback for retry action
   */
  onRetry?: () => void;
  /**
   * Optional icon to display
   */
  icon?: React.ReactNode;
  /**
   * Additional button props
   */
  buttonProps?: Partial<ButtonProps>;
}

/**
 * A reusable error state component with optional retry functionality
 */
export const ErrorState = React.memo<ErrorStateProps>(function ErrorState(props) {
  const {
    style,
    title,
    titleTx,
    titleTxOptions,
    message,
    messageTx,
    messageTxOptions,
    retryText,
    retryTx,
    retryTxOptions,
    onRetry,
    icon,
    buttonProps,
  } = props;

  const { themed } = useAppTheme();

  return (
    <View style={[themed($container), style]}>
      {icon && <View style={themed($iconContainer)}>{icon}</View>}

      {(title || titleTx) && (
        <Text preset="subheading" text={title} tx={titleTx} txOptions={titleTxOptions} style={themed($title)} />
      )}

      {(message || messageTx) && (
        <Text text={message} tx={messageTx} txOptions={messageTxOptions} style={themed($message)} size="sm" />
      )}

      {onRetry && (
        <Button
          text={retryText}
          tx={retryTx ?? "common:retry"}
          txOptions={retryTxOptions}
          onPress={onRetry}
          preset="filled"
          style={themed($retryButton)}
          {...buttonProps}
        />
      )}
    </View>
  );
});

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
  paddingHorizontal: spacing.lg,
});

const $iconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
});

const $title: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  textAlign: "center",
  marginBottom: spacing.sm,
});

const $message: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  textAlign: "center",
  marginBottom: spacing.lg,
});

const $retryButton: ThemedStyle<ViewStyle> = () => ({
  minWidth: 120,
});
