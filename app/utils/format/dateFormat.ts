/**
 * Date formatting utilities
 */

/**
 * Formats a Unix timestamp to a relative time string
 * @param timestamp Unix timestamp in seconds
 * @returns Relative time string (e.g., "2 days ago", "3 hours ago")
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Math.floor(Date.now() / 1000);
  const diffSeconds = now - timestamp;

  if (diffSeconds < 60) {
    return "Just now";
  }

  const diffMinutes = Math.floor(diffSeconds / 60);
  if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes === 1 ? "" : "s"} ago`;
  }

  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours < 24) {
    return `${diffHours} hour${diffHours === 1 ? "" : "s"} ago`;
  }

  const diffDays = Math.floor(diffHours / 24);
  if (diffDays < 7) {
    return `${diffDays} day${diffDays === 1 ? "" : "s"} ago`;
  }

  const diffWeeks = Math.floor(diffDays / 7);
  if (diffWeeks < 4) {
    return `${diffWeeks} week${diffWeeks === 1 ? "" : "s"} ago`;
  }

  // For older dates, show absolute date
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString();
}

/**
 * Formats a Unix timestamp to a readable date string
 * @param timestamp Unix timestamp in seconds
 * @param options Intl.DateTimeFormatOptions for formatting
 * @returns Formatted date string
 */
export function formatAbsoluteDate(
  timestamp: number,
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  },
): string {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString(undefined, options);
}

/**
 * Formats a Unix timestamp to a readable date and time string
 * @param timestamp Unix timestamp in seconds
 * @param options Intl.DateTimeFormatOptions for formatting
 * @returns Formatted date and time string
 */
export function formatDateTime(
  timestamp: number,
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  },
): string {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString(undefined, options);
}

/**
 * Checks if a timestamp is from today
 * @param timestamp Unix timestamp in seconds
 * @returns true if the timestamp is from today
 */
export function isToday(timestamp: number): boolean {
  const date = new Date(timestamp * 1000);
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
}

/**
 * Checks if a timestamp is from yesterday
 * @param timestamp Unix timestamp in seconds
 * @returns true if the timestamp is from yesterday
 */
export function isYesterday(timestamp: number): boolean {
  const date = new Date(timestamp * 1000);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  );
}
