/**
 * Duration formatting utilities
 */

/**
 * Formats duration in minutes to a readable string
 * @param minutes Duration in minutes
 * @returns Formatted duration string (e.g., "25.5 min")
 */
export function formatDuration(minutes: number): string {
  return `${minutes} min`;
}

/**
 * Formats duration in seconds to a readable string (MM:SS format)
 * @param seconds Duration in seconds
 * @returns Formatted duration string (e.g., "25:30")
 */
export function formatMatchDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

/**
 * Formats time played in seconds to a readable string
 * @param seconds Time played in seconds
 * @returns Formatted time played string (e.g., "2d 3h", "1h 30m", "30m")
 */
export function formatTimePlayed(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return hours % 24 === 0 ? `${days}d` : `${days}d ${hours % 24}h`;
  } else if (hours > 0) {
    return minutes % 60 === 0 ? `${hours}h` : `${hours}h ${minutes % 60}m`;
  } else {
    return `${minutes}m`;
  }
}

/**
 * Formats duration in seconds to hours and minutes
 * @param seconds Duration in seconds
 * @returns Formatted duration string (e.g., "2h 30m", "45m")
 */
export function formatHoursMinutes(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours > 0) {
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  }
  return `${minutes}m`;
}

/**
 * Formats duration in milliseconds to a readable string
 * @param milliseconds Duration in milliseconds
 * @returns Formatted duration string
 */
export function formatMilliseconds(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }

  const seconds = Math.floor(milliseconds / 1000);
  const remainingMs = milliseconds % 1000;

  if (seconds < 60) {
    return remainingMs > 0 ? `${seconds}.${Math.floor(remainingMs / 100)}s` : `${seconds}s`;
  }

  return formatMatchDuration(seconds);
}

/**
 * Converts seconds to different time units
 * @param seconds Duration in seconds
 * @returns Object with time units
 */
export function parseTimeUnits(seconds: number): {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
} {
  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);
  const remainingSeconds = seconds % 60;

  return {
    days,
    hours,
    minutes,
    seconds: remainingSeconds,
  };
}

/**
 * Formats a duration with full precision (days, hours, minutes, seconds)
 * @param seconds Duration in seconds
 * @param showSeconds Whether to include seconds in the output
 * @returns Formatted duration string
 */
export function formatFullDuration(seconds: number, showSeconds: boolean = false): string {
  const { days, hours, minutes, seconds: secs } = parseTimeUnits(seconds);
  const parts: string[] = [];

  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (showSeconds && secs > 0) parts.push(`${secs}s`);

  return parts.length > 0 ? parts.join(" ") : "0m";
}
