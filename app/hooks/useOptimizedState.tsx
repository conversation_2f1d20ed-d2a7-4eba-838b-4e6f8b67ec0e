import { useCallback, useRef, useState } from "react";

/**
 * Optimized state hook that prevents unnecessary re-renders
 * by using shallow comparison for object/array updates
 */
export function useOptimizedState<T>(initialState: T) {
  const [state, setState] = useState<T>(initialState);
  const stateRef = useRef<T>(state);
  stateRef.current = state;

  const setOptimizedState = useCallback((newState: T | ((prevState: T) => T)) => {
    setState((prevState) => {
      const nextState = typeof newState === "function" ? (newState as (prevState: T) => T)(prevState) : newState;

      // Shallow comparison to prevent unnecessary updates
      if (shallowEqual(prevState, nextState)) {
        return prevState;
      }

      return nextState;
    });
  }, []);

  return [state, setOptimizedState] as const;
}

/**
 * Hook for managing loading states with automatic cleanup
 */
export function useLoadingState(initialLoading: boolean = false) {
  const [isLoading, setIsLoading] = useState(initialLoading);
  const loadingRef = useRef<Set<string>>(new Set());

  const startLoading = useCallback((key: string = "default") => {
    loadingRef.current.add(key);
    setIsLoading(true);
  }, []);

  const stopLoading = useCallback((key: string = "default") => {
    loadingRef.current.delete(key);
    if (loadingRef.current.size === 0) {
      setIsLoading(false);
    }
  }, []);

  const resetLoading = useCallback(() => {
    loadingRef.current.clear();
    setIsLoading(false);
  }, []);

  return {
    isLoading,
    startLoading,
    stopLoading,
    resetLoading,
  };
}

/**
 * Hook for managing error states with automatic retry logic
 */
export function useErrorState<T = Error>() {
  const [error, setError] = useState<T | null>(null);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  const setErrorWithRetry = useCallback((newError: T | null, canRetry: boolean = true) => {
    if (newError && canRetry && retryCountRef.current < maxRetries) {
      retryCountRef.current += 1;
      // Don't set error immediately, allow for retry
      setTimeout(() => {
        if (retryCountRef.current >= maxRetries) {
          setError(newError);
        }
      }, 1000 * retryCountRef.current); // Exponential backoff
    } else {
      setError(newError);
      if (!newError) {
        retryCountRef.current = 0; // Reset retry count on success
      }
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
    retryCountRef.current = 0;
  }, []);

  return {
    error,
    setError: setErrorWithRetry,
    clearError,
    retryCount: retryCountRef.current,
    canRetry: retryCountRef.current < maxRetries,
  };
}

/**
 * Hook for managing async operations with loading and error states
 */
export function useAsyncOperation<T, P extends any[] = []>() {
  const { isLoading, startLoading, stopLoading } = useLoadingState();
  const { error, setError, clearError } = useErrorState();
  const [data, setData] = useState<T | null>(null);

  const execute = useCallback(
    async (operation: (...args: P) => Promise<T>, ...args: P) => {
      try {
        clearError();
        startLoading();
        const result = await operation(...args);
        setData(result);
        return result;
      } catch (err) {
        setError(err as Error);
        throw err;
      } finally {
        stopLoading();
      }
    },
    [clearError, startLoading, stopLoading, setError],
  );

  const reset = useCallback(() => {
    setData(null);
    clearError();
    stopLoading();
  }, [clearError, stopLoading]);

  return {
    data,
    isLoading,
    error,
    execute,
    reset,
  };
}

/**
 * Hook for debouncing values to prevent excessive updates
 */
export function useDebouncedValue<T>(value: T, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  const updateDebouncedValue = useCallback(() => {
    setDebouncedValue(value);
  }, [value]);

  const timeoutRef = useRef<NodeJS.Timeout>();

  // Update debounced value after delay
  React.useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(updateDebouncedValue, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, delay, updateDebouncedValue]);

  return debouncedValue;
}

/**
 * Shallow comparison utility function
 */
function shallowEqual<T>(obj1: T, obj2: T): boolean {
  if (obj1 === obj2) {
    return true;
  }

  if (typeof obj1 !== "object" || typeof obj2 !== "object" || obj1 === null || obj2 === null) {
    return false;
  }

  const keys1 = Object.keys(obj1 as object);
  const keys2 = Object.keys(obj2 as object);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!(key in (obj2 as object)) || (obj1 as any)[key] !== (obj2 as any)[key]) {
      return false;
    }
  }

  return true;
}
