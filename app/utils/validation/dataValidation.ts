/**
 * General data validation utilities
 */

import { isNonEmptyArray, isValidNumber, isValidString } from "./typeGuards";

/**
 * Validates if an array contains only valid items of a specific type
 * @param array The array to validate
 * @param validator Function to validate each item
 * @returns true if all items are valid
 */
export function validateArrayItems<T>(array: unknown, validator: (item: unknown) => item is T): array is T[] {
  if (!isNonEmptyArray(array)) {
    return false;
  }

  return array.every(validator);
}

/**
 * Validates if a pagination object has valid parameters
 * @param pagination The pagination object to validate
 * @returns true if pagination is valid
 */
export function isValidPagination(pagination: unknown): pagination is {
  page: number;
  limit: number;
  total?: number;
} {
  if (typeof pagination !== "object" || pagination === null) {
    return false;
  }

  const p = pagination as Record<string, unknown>;

  return (
    isValidNumber(p.page) &&
    p.page >= 1 &&
    isValidNumber(p.limit) &&
    p.limit >= 1 &&
    p.limit <= 100 &&
    (p.total === undefined || (isValidNumber(p.total) && p.total >= 0))
  );
}

/**
 * Validates if a sort configuration is valid
 * @param sort The sort configuration to validate
 * @returns true if sort configuration is valid
 */
export function isValidSort(sort: unknown): sort is {
  field: string;
  direction: "asc" | "desc";
} {
  if (typeof sort !== "object" || sort === null) {
    return false;
  }

  const s = sort as Record<string, unknown>;

  return isValidString(s.field) && (s.direction === "asc" || s.direction === "desc");
}

/**
 * Validates if a filter object has valid structure
 * @param filter The filter object to validate
 * @returns true if filter is valid
 */
export function isValidFilter(filter: unknown): filter is Record<string, unknown> {
  if (typeof filter !== "object" || filter === null) {
    return false;
  }

  // Basic validation - filter should be an object with string keys
  const f = filter as Record<string, unknown>;

  return Object.keys(f).every((key) => typeof key === "string");
}

/**
 * Validates if a search query is valid
 * @param query The search query to validate
 * @returns true if query is valid
 */
export function isValidSearchQuery(query: unknown): query is string {
  if (!isValidString(query)) {
    return false;
  }

  // Query should be reasonable length and not just whitespace
  const trimmed = query.trim();
  return trimmed.length >= 1 && trimmed.length <= 100;
}

/**
 * Validates if a date range is valid
 * @param dateRange The date range object to validate
 * @returns true if date range is valid
 */
export function isValidDateRange(dateRange: unknown): dateRange is {
  start: Date;
  end: Date;
} {
  if (typeof dateRange !== "object" || dateRange === null) {
    return false;
  }

  const dr = dateRange as Record<string, unknown>;

  return (
    dr.start instanceof Date &&
    !Number.isNaN(dr.start.getTime()) &&
    dr.end instanceof Date &&
    !Number.isNaN(dr.end.getTime()) &&
    dr.end >= dr.start
  );
}

/**
 * Validates if an API response has the expected structure
 * @param response The API response to validate
 * @returns true if response structure is valid
 */
export function isValidApiResponse<T>(
  response: unknown,
  dataValidator?: (data: unknown) => data is T,
): response is { data: T; ok: boolean } {
  if (typeof response !== "object" || response === null) {
    return false;
  }

  const r = response as Record<string, unknown>;

  const hasValidStructure = typeof r.ok === "boolean" && "data" in r;

  if (!hasValidStructure) {
    return false;
  }

  // If a data validator is provided, use it
  if (dataValidator) {
    return dataValidator(r.data);
  }

  return true;
}

/**
 * Validates if a configuration object has required fields
 * @param config The configuration object to validate
 * @param requiredFields Array of required field names
 * @returns true if all required fields are present
 */
export function hasRequiredFields(config: unknown, requiredFields: string[]): config is Record<string, unknown> {
  if (typeof config !== "object" || config === null) {
    return false;
  }

  const c = config as Record<string, unknown>;

  return requiredFields.every((field) => field in c && c[field] !== undefined);
}

/**
 * Validates if a value is within a specified range
 * @param value The value to validate
 * @param min Minimum value (inclusive)
 * @param max Maximum value (inclusive)
 * @returns true if value is within range
 */
export function isInRange(value: unknown, min: number, max: number): value is number {
  return isValidNumber(value) && value >= min && value <= max;
}
