/**
 * React Query helper utilities
 */

import type { UseMutationOptions, UseQueryOptions } from "@tanstack/react-query";
import type { ApiResponse } from "apisauce";
import { type ApiError, handleApiResponse } from "./errorHandling";

/**
 * Default query options for consistent behavior
 */
export const DEFAULT_QUERY_OPTIONS = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  retry: (failureCount: number, error: unknown) => {
    // Don't retry client errors (4xx)
    if (error && typeof error === "object" && "statusCode" in error) {
      const statusCode = (error as { statusCode?: number }).statusCode;
      if (statusCode && statusCode >= 400 && statusCode < 500) {
        return false;
      }
    }
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
} as const;

/**
 * Creates query options for API calls with consistent error handling
 * @param queryFn Function that returns an API response
 * @param options Additional query options
 * @returns Query options with error handling
 */
export function createQueryOptions<T>(
  queryFn: () => Promise<ApiResponse<T>>,
  options: Partial<UseQueryOptions<T, ApiError>> = {},
): UseQueryOptions<T, ApiError> {
  return {
    ...DEFAULT_QUERY_OPTIONS,
    queryFn: async () => {
      const response = await queryFn();
      return handleApiResponse(response);
    },
    ...options,
  };
}

/**
 * Creates mutation options for API calls with consistent error handling
 * @param mutationFn Function that performs the mutation
 * @param options Additional mutation options
 * @returns Mutation options with error handling
 */
export function createMutationOptions<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<ApiResponse<TData>>,
  options: Partial<UseMutationOptions<TData, ApiError, TVariables>> = {},
): UseMutationOptions<TData, ApiError, TVariables> {
  return {
    mutationFn: async (variables: TVariables) => {
      const response = await mutationFn(variables);
      return handleApiResponse(response);
    },
    ...options,
  };
}

/**
 * Query key factory for consistent cache key generation
 */
export const queryKeys = {
  // Player-related queries
  player: (playerId: number) => ["player", playerId] as const,
  playerProfile: (playerId: number) => ["player", playerId, "profile"] as const,
  playerMatches: (playerId: number, filters?: Record<string, unknown>) =>
    ["player", playerId, "matches", filters] as const,
  playerStats: (playerId: number, timeRange?: { start: number; end: number }) =>
    ["player", playerId, "stats", timeRange] as const,
  playerHeroes: (playerId: number, timeRange?: { start: number; end: number }) =>
    ["player", playerId, "heroes", timeRange] as const,

  // Match-related queries
  match: (matchId: number) => ["match", matchId] as const,
  matchMetadata: (matchId: number) => ["match", matchId, "metadata"] as const,
  matchDetails: (matchId: number) => ["match", matchId, "details"] as const,

  // Hero-related queries
  heroes: () => ["heroes"] as const,
  hero: (heroId: number) => ["hero", heroId] as const,
  heroStats: (heroId: number, playerId?: number) => ["hero", heroId, "stats", playerId] as const,

  // Assets queries
  assets: {
    heroes: () => ["assets", "heroes"] as const,
    ranks: () => ["assets", "ranks"] as const,
    items: () => ["assets", "items"] as const,
    map: () => ["assets", "map"] as const,
  },

  // Search queries
  search: {
    players: (query: string) => ["search", "players", query] as const,
    matches: (filters: Record<string, unknown>) => ["search", "matches", filters] as const,
  },
} as const;

/**
 * Helper to invalidate related queries when data changes
 */
export const invalidationPatterns = {
  // Invalidate all player data
  player: (playerId: number) => [["player", playerId]],

  // Invalidate player matches and stats
  playerData: (playerId: number) => [
    ["player", playerId, "matches"],
    ["player", playerId, "stats"],
    ["player", playerId, "heroes"],
  ],

  // Invalidate match data
  match: (matchId: number) => [["match", matchId]],

  // Invalidate all assets
  assets: () => [["assets"]],
} as const;

/**
 * Creates a query key with optional filters
 * @param baseKey Base query key
 * @param filters Optional filters to append
 * @returns Complete query key
 */
export function createQueryKey(baseKey: readonly unknown[], filters?: Record<string, unknown>) {
  if (!filters || Object.keys(filters).length === 0) {
    return baseKey;
  }
  return [...baseKey, filters] as const;
}

/**
 * Helper to create time-based query keys
 * @param baseKey Base query key
 * @param timeRange Optional time range
 * @returns Query key with time range
 */
export function createTimeBasedQueryKey(baseKey: readonly unknown[], timeRange?: { start: number; end: number }) {
  return timeRange ? ([...baseKey, timeRange] as const) : baseKey;
}

/**
 * Default options for assets queries (longer cache time)
 */
export const ASSETS_QUERY_OPTIONS = {
  staleTime: 24 * 60 * 60 * 1000, // 24 hours
  gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  networkMode: "offlineFirst" as const,
} as const;

/**
 * Default options for real-time data queries (shorter cache time)
 */
export const REALTIME_QUERY_OPTIONS = {
  staleTime: 30 * 1000, // 30 seconds
  gcTime: 5 * 60 * 1000, // 5 minutes
  refetchInterval: 60 * 1000, // 1 minute
} as const;
