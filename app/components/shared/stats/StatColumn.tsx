import React from "react";
import { type TextStyle, View, type ViewStyle } from "react-native";
import { Text, type TextProps } from "@/components/ui/Text";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface StatColumnProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * The label text to display
   */
  label?: string;
  /**
   * Label text which is looked up via i18n.
   */
  labelTx?: TextProps["tx"];
  /**
   * Optional label options to pass to i18n.
   */
  labelTxOptions?: TextProps["txOptions"];
  /**
   * The value to display
   */
  value: string | number | React.ReactNode;
  /**
   * Optional color override for the value
   */
  valueColor?: string;
  /**
   * Alignment of the content
   */
  alignment?: "left" | "center" | "right";
  /**
   * Size of the value text
   */
  valueSize?: "xs" | "sm" | "md" | "lg";
  /**
   * Whether to make the value bold
   */
  valueBold?: boolean;
}

/**
 * A reusable stat column component for displaying label-value pairs vertically
 */
export const StatColumn = React.memo<StatColumnProps>(function StatColumn(props) {
  const {
    style,
    label,
    labelTx,
    labelTxOptions,
    value,
    valueColor,
    alignment = "center",
    valueSize = "xs",
    valueBold = true,
  } = props;

  const { themed } = useAppTheme();

  const containerStyle = [
    themed($container),
    alignment === "left" && themed($alignLeft),
    alignment === "right" && themed($alignRight),
    style,
  ];

  return (
    <View style={containerStyle}>
      {(label || labelTx) && (
        <Text text={label} tx={labelTx} txOptions={labelTxOptions} style={themed($label)} size="xxs" />
      )}

      {React.isValidElement(value) ? (
        value
      ) : (
        <Text
          style={[themed($value), valueBold && themed($valueBold), valueColor && { color: valueColor }]}
          size={valueSize}
        >
          {value}
        </Text>
      )}
    </View>
  );
});

const $container: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  gap: 4,
});

const $alignLeft: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-start",
});

const $alignRight: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-end",
});

const $label: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
});

const $value: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
});

const $valueBold: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.semiBold,
});
