import React from "react";
import { ActivityIndicator, type TextStyle, View, type ViewStyle } from "react-native";
import { Text, type TextProps } from "@/components/ui/Text";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface LoadingStateProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * Optional loading message
   */
  message?: string;
  /**
   * Loading message which is looked up via i18n.
   */
  messageTx?: TextProps["tx"];
  /**
   * Optional message options to pass to i18n.
   */
  messageTxOptions?: TextProps["txOptions"];
  /**
   * Size of the loading indicator
   */
  size?: "small" | "large";
  /**
   * Color of the loading indicator
   */
  color?: string;
}

/**
 * A reusable loading state component with optional message
 */
export const LoadingState = React.memo<LoadingStateProps>(function LoadingState(props) {
  const { style, message, messageTx, messageTxOptions, size = "large", color } = props;

  const { themed, theme } = useAppTheme();

  return (
    <View style={[themed($container), style]}>
      <ActivityIndicator size={size} color={color ?? theme.colors.tint} style={themed($indicator)} />
      {(message || messageTx) && (
        <Text text={message} tx={messageTx} txOptions={messageTxOptions} style={themed($message)} size="sm" />
      )}
    </View>
  );
});

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
});

const $indicator: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm,
});

const $message: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  textAlign: "center",
});
