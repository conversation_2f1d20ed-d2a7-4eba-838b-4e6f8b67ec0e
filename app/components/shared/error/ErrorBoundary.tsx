import React, { Component, type ErrorInfo, type ReactNode } from "react";
import { View, type ViewStyle } from "react-native";
import { ErrorState } from "../states/ErrorState";

interface ErrorBoundaryProps {
  /**
   * Children components to wrap
   */
  children: ReactNode;
  /**
   * Fallback component to render when an error occurs
   */
  fallback?: (error: Error, errorInfo: ErrorInfo) => ReactNode;
  /**
   * Callback when an error occurs
   */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  /**
   * Whether to show retry button
   */
  showRetry?: boolean;
  /**
   * Custom error message
   */
  errorMessage?: string;
  /**
   * Custom error title
   */
  errorTitle?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error boundary component that catches JavaScript errors anywhere in the child component tree
 * and displays a fallback UI instead of crashing the entire app
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);

    // Log the error for debugging
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  handleRetry = () => {
    // Reset the error state to retry rendering
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.state.errorInfo!);
      }

      // Default fallback UI
      return (
        <View style={$errorContainer}>
          <ErrorState
            title={this.props.errorTitle || "Something went wrong"}
            message={this.props.errorMessage || "An unexpected error occurred. Please try again."}
            retryText={this.props.showRetry ? "Try Again" : undefined}
            onRetry={this.props.showRetry ? this.handleRetry : undefined}
          />
        </View>
      );
    }

    // No error, render children normally
    return this.props.children;
  }
}

/**
 * Hook-based error boundary for functional components
 * Note: This is a wrapper around the class-based ErrorBoundary
 */
export interface ErrorBoundaryWrapperProps extends Omit<ErrorBoundaryProps, "children"> {
  children: ReactNode;
}

export const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = (props) => {
  return <ErrorBoundary {...props} />;
};

/**
 * Higher-order component that wraps a component with an error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, "children">,
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} ref={ref} />
    </ErrorBoundary>
  ));

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Hook to manually trigger error boundary
 * Useful for handling async errors that don't get caught by error boundaries
 */
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  // Throw the error to trigger error boundary
  if (error) {
    throw error;
  }

  return { handleError, resetError };
}

const $errorContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: 20,
};
