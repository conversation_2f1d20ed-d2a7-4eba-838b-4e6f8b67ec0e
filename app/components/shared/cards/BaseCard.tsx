import React from "react";
import { TouchableOpacity, type TouchableOpacityProps, View, type ViewStyle } from "react-native";
import type { StyleProp } from "react-native/Libraries/StyleSheet/StyleSheet";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface BaseCardProps extends Omit<TouchableOpacityProps, "style"> {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>;
  /**
   * Whether the card should be pressable
   */
  pressable?: boolean;
  /**
   * Card variant for different styling
   */
  variant?: "default" | "elevated" | "outlined";
  /**
   * Children to render inside the card
   */
  children: React.ReactNode;
}

/**
 * Base card component that provides consistent styling and behavior
 * Can be used as a foundation for other card components
 */
export const BaseCard = React.memo<BaseCardProps>(function BaseCard(props) {
  const { style, pressable = false, variant = "default", children, ...touchableProps } = props;
  const { themed } = useAppTheme();

  const cardStyle: StyleProp<ViewStyle>[] = [themed($baseCard), themed($variants[variant]), style];

  if (pressable) {
    return (
      <TouchableOpacity style={cardStyle} activeOpacity={0.7} {...touchableProps}>
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={cardStyle}>{children}</View>;
});

const $baseCard: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  padding: spacing.sm,
});

const $variants = {
  default: () => ({}) as StyleProp<ViewStyle>,
  elevated: () =>
    ({
      elevation: 1,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
    }) as StyleProp<ViewStyle>,
  outlined: ({ colors }: { colors: any }) =>
    ({
      borderWidth: 1,
      borderColor: colors.border,
    }) as StyleProp<ViewStyle>,
};
