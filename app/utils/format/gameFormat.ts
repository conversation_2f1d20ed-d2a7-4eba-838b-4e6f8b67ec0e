/**
 * Game-specific formatting utilities
 */

/**
 * Parses match mode number to readable string
 * @param matchMode The match mode number
 * @returns Human-readable match mode string
 */
export function parseMatchMode(matchMode?: number): string {
  switch (matchMode) {
    case 1:
      return "Unranked";
    case 2:
      return "Private Lobby";
    case 3:
      return "Co-op Bot";
    case 4:
      return "Ranked";
    case 5:
      return "Server Test";
    case 6:
      return "Tutorial";
    case 7:
      return "Hero Labs";
    default:
      return "Unknown";
  }
}

/**
 * Formats a win rate as a percentage
 * @param wins Number of wins
 * @param total Total number of games
 * @param decimals Number of decimal places (default: 1)
 * @returns Formatted win rate string
 */
export function formatWinRate(wins: number, total: number, decimals: number = 1): string {
  if (total === 0) return "0%";
  const rate = (wins / total) * 100;
  return `${rate.toFixed(decimals)}%`;
}

/**
 * Formats KDA as a string
 * @param kills Number of kills
 * @param deaths Number of deaths
 * @param assists Number of assists
 * @returns Formatted KDA string (e.g., "10/2/5")
 */
export function formatKDAString(kills: number, deaths: number, assists: number): string {
  return `${kills}/${deaths}/${assists}`;
}

/**
 * Formats a rank badge number to tier and subtier
 * @param badgeNumber The badge number
 * @returns Object with tier and subtier
 */
export function parseBadgeNumber(badgeNumber: number): { tier: number; subtier: number } | null {
  if (!badgeNumber || badgeNumber <= 0) {
    return null;
  }

  const badgeStr = badgeNumber.toString();
  const subtier = Number.parseInt(badgeStr.slice(-1), 10);
  const tier = Number.parseInt(badgeStr.slice(0, -1), 10);

  if (Number.isNaN(tier) || Number.isNaN(subtier)) {
    return null;
  }

  return { tier, subtier };
}

/**
 * Formats a hero level with appropriate styling
 * @param level The hero level
 * @returns Formatted level string
 */
export function formatHeroLevel(level: number): string {
  return `Lv. ${level}`;
}

/**
 * Formats last hits and denies
 * @param lastHits Number of last hits
 * @param denies Number of denies
 * @returns Formatted string (e.g., "150/25")
 */
export function formatLastHitsDenies(lastHits: number, denies: number): string {
  return `${lastHits}/${denies}`;
}

/**
 * Formats a match result to a readable string
 * @param isWin Whether the match was won
 * @returns "Victory" or "Defeat"
 */
export function formatMatchResult(isWin: boolean): string {
  return isWin ? "Victory" : "Defeat";
}

/**
 * Gets color for match result
 * @param isWin Whether the match was won
 * @returns Color string for styling
 */
export function getMatchResultColor(isWin: boolean): string {
  return isWin ? "#22c55e" : "#ef4444"; // green for win, red for loss
}

/**
 * Formats team composition (e.g., for displaying team members)
 * @param teamSize Number of players in team
 * @param currentPlayer Current player position (1-based)
 * @returns Formatted team position string
 */
export function formatTeamPosition(teamSize: number, currentPlayer: number): string {
  return `${currentPlayer}/${teamSize}`;
}

/**
 * Formats objective completion percentage
 * @param completed Number of completed objectives
 * @param total Total number of objectives
 * @returns Formatted percentage string
 */
export function formatObjectiveCompletion(completed: number, total: number): string {
  if (total === 0) return "0%";
  const percentage = (completed / total) * 100;
  return `${Math.round(percentage)}%`;
}
