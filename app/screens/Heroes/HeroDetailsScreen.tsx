import React, { type FC, useCallback, useMemo } from "react";
import { type TextStyle, View, type ViewStyle } from "react-native";
import { usePlayerSelected, useTimeRangeSelected } from "@/app";
import { HeroImage } from "@/components/heroes/HeroImage";
import { HeroName } from "@/components/heroes/HeroName";
import { TimeRangeSelect } from "@/components/select/TimeRangeSelect";
import {
  EmptyState,
  ErrorBoundary,
  ErrorState,
  ListContainer,
  LoadingState,
  OptimizedFlatList,
  OptimizedMatchItem,
  SectionHeader,
  StatCard,
  StatsGrid
} from "@/components/shared";
import { Screen } from "@/components/ui/Screen";
import { Text } from "@/components/ui/Text";
import { useAssetsHero } from "@/hooks/useAssetsHeroes";
import { useHeroStats } from "@/hooks/useHeroStats";
import { useMatchHistory } from "@/hooks/useMatchHistory";
import { useOptimizedState } from "@/hooks/useOptimizedState";
import type { HeroesStackScreenProps } from "@/navigators/HeroesNavigator";
import type { MatchHistory } from "@/services/api/types/match_history";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";
import { formatPercentage, formatTimePlayed } from "@/utils/format";
import { scaleColor } from "@/utils/scaleColor";

export const RefactoredHeroDetailsScreen: FC<HeroesStackScreenProps<"Details">> = React.memo(
  function RefactoredHeroDetailsScreen(props) {
    const { themed } = useAppTheme();
    const { navigation } = props;

    // Global state
    const [timeRange] = useTimeRangeSelected();
    const [player] = usePlayerSelected();

    // Local state
    const [selectedMatch, setSelectedMatch] = useOptimizedState<MatchHistory | null>(null);

    // Route params
    const heroId = props.route.params.heroId;

    // Time calculations
    const { minUnixTimestamp } = useMemo(() => {
      const now = Math.floor(Date.now() / 1000);
      const nextFullHour = Math.ceil(now / 3600) * 3600;
      return {
        minUnixTimestamp: timeRange.value ? nextFullHour - timeRange.value : 0,
      };
    }, [timeRange.value]);

    // Data fetching
    const { data: heroAsset, isLoading: isHeroLoading, error: heroError } = useAssetsHero(heroId);
    const {
      data: heroStatsAll,
      isLoading: isHeroStatsLoading,
      error: heroStatsError,
    } = useHeroStats(player?.account_id ?? null, minUnixTimestamp);
    const {
      data: matchHistory,
      isLoading: isMatchHistoryLoading,
      error: matchHistoryError,
    } = useMatchHistory(player?.account_id ?? null);

    // Computed data
    const { heroStat, heroMatches, statsCards } = useMemo(() => {
      const heroStat = heroStatsAll?.find((h) => h.hero_id === heroId);
      const heroMatches = (matchHistory ?? [])
        .filter((m) => !minUnixTimestamp || m.start_time >= minUnixTimestamp)
        .filter((m) => m.hero_id === heroId)
        .sort((a, b) => b.start_time - a.start_time);

      const statsCards = heroStat
        ? [
            {
              id: "matches",
              title: "Matches",
              value: heroStat.matches_played,
              subtitle: "played",
            },
            {
              id: "winRate",
              title: "Win Rate",
              value: formatPercentage(heroStat.wins / heroStat.matches_played),
              valueColor: scaleColor(heroStat.wins / heroStat.matches_played, 0, 100),
            },
            {
              id: "avgKills",
              title: "Avg Kills",
              value: (heroStat.kills / heroStat.matches_played).toFixed(1),
            },
            {
              id: "avgDeaths",
              title: "Avg Deaths",
              value: (heroStat.deaths / heroStat.matches_played).toFixed(1),
            },
            {
              id: "avgAssists",
              title: "Avg Assists",
              value: (heroStat.assists / heroStat.matches_played).toFixed(1),
            },
            {
              id: "timePlayed",
              title: "Time Played",
              value: formatTimePlayed(heroStat.time_played),
            },
          ]
        : [];

      return { heroStat, heroMatches, statsCards };
    }, [heroStatsAll, heroId, matchHistory, minUnixTimestamp]);

    // Event handlers
    const handleMatchPress = useCallback(
      (match: MatchHistory) => {
        setSelectedMatch(match);
        navigation.navigate("MatchDetails", { matchId: match.match_id });
      },
      [navigation, setSelectedMatch],
    );

    const handleRetry = useCallback(() => {
      // Implement retry logic here
      console.log("Retrying data fetch...");
    }, []);

    // Render functions
    const renderMatchItem = useCallback(
      ({ item }: { item: MatchHistory }) => (
        <OptimizedMatchItem
          match={item}
          onPress={handleMatchPress}
          isSelected={selectedMatch?.match_id === item.match_id}
        />
      ),
      [handleMatchPress, selectedMatch?.match_id],
    );

    const keyExtractor = useCallback((item: MatchHistory) => item.match_id.toString(), []);

    // Loading state
    const isLoading = isHeroLoading || isHeroStatsLoading || isMatchHistoryLoading;
    const error = heroError || heroStatsError || matchHistoryError;

    if (isLoading) {
      return (
        <Screen preset="scroll" safeAreaEdges={["top"]}>
          <LoadingState message="Loading hero details..." />
        </Screen>
      );
    }

    if (error) {
      return (
        <Screen preset="scroll" safeAreaEdges={["top"]}>
          <ErrorState
            title="Failed to load hero details"
            message="Please check your connection and try again."
            onRetry={handleRetry}
          />
        </Screen>
      );
    }

    return (
      <ErrorBoundary
        errorTitle="Hero Details Error"
        errorMessage="Something went wrong while displaying hero details."
        showRetry
      >
        <Screen preset="scroll" safeAreaEdges={["top"]}>
          {/* Hero Header */}
          <View style={themed($heroHeader)}>
            <HeroImage heroId={heroId} size={80} />
            <View style={themed($heroInfo)}>
              <Text preset="heading" style={themed($heroName)}>
                <HeroName heroId={heroId} />
              </Text>
              {heroAsset && (
                <Text size="sm" style={themed($heroDescription)}>
                  {heroAsset.description.role}
                </Text>
              )}
            </View>
          </View>

          {/* Time Range Selector */}
          <View style={themed($timeRangeContainer)}>
            <TimeRangeSelect />
          </View>

          {/* Stats Section */}
          <SectionHeader titleTx="heroDetailsScreen:stats" style={themed($sectionHeader)} />

          {heroStat ? (
            <StatsGrid columns={2} gap="sm">
              {statsCards.map((stat) => (
                <StatCard
                  key={stat.id}
                  title={stat.title}
                  value={stat.value}
                  subtitle={stat.subtitle}
                  valueColor={stat.valueColor}
                />
              ))}
            </StatsGrid>
          ) : (
            <EmptyState title="No stats available" message="Play some matches with this hero to see statistics." />
          )}

          {/* Matches Section */}
          <SectionHeader titleTx="heroDetailsScreen:heroMatches" style={themed($sectionHeader)} />

          <ListContainer>
            <OptimizedFlatList
              data={heroMatches}
              renderItem={renderMatchItem}
              keyExtractor={keyExtractor}
              emptyState={{
                title: "No matches found",
                message: "No matches with this hero in the selected time range.",
              }}
              performance={{
                estimatedItemSize: 120,
                maxToRenderPerBatch: 10,
                windowSize: 10,
                initialNumToRender: 5,
              }}
            />
          </ListContainer>
        </Screen>
      </ErrorBoundary>
    );
  },
);

// Styled components
const $heroHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  padding: spacing.lg,
});

const $heroInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  marginLeft: spacing.md,
});

const $heroName: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
});

const $heroDescription: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
});

const $timeRangeContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  marginBottom: spacing.md,
});

const $sectionHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  marginTop: spacing.lg,
  marginBottom: spacing.md,
});
