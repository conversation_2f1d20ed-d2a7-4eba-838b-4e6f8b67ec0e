import React from "react";
import { View, type ViewStyle } from "react-native";
import { Button, type ButtonProps } from "@/components/ui/Button";
import { Text, type TextProps } from "@/components/ui/Text";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface EmptyStateProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * Empty state title
   */
  title?: string;
  /**
   * Empty state title which is looked up via i18n.
   */
  titleTx?: TextProps["tx"];
  /**
   * Optional title options to pass to i18n.
   */
  titleTxOptions?: TextProps["txOptions"];
  /**
   * Empty state message
   */
  message?: string;
  /**
   * Empty state message which is looked up via i18n.
   */
  messageTx?: TextProps["tx"];
  /**
   * Optional message options to pass to i18n.
   */
  messageTxOptions?: TextProps["txOptions"];
  /**
   * Optional action button text
   */
  actionText?: string;
  /**
   * Action button text which is looked up via i18n.
   */
  actionTx?: TextProps["tx"];
  /**
   * Optional action text options to pass to i18n.
   */
  actionTxOptions?: TextProps["txOptions"];
  /**
   * Callback for action button
   */
  onAction?: () => void;
  /**
   * Optional icon or illustration to display
   */
  icon?: React.ReactNode;
  /**
   * Additional button props
   */
  buttonProps?: Partial<ButtonProps>;
}

/**
 * A reusable empty state component with optional action button
 */
export const EmptyState = React.memo<EmptyStateProps>(function EmptyState(props) {
  const {
    style,
    title,
    titleTx,
    titleTxOptions,
    message,
    messageTx,
    messageTxOptions,
    actionText,
    actionTx,
    actionTxOptions,
    onAction,
    icon,
    buttonProps,
  } = props;

  const { themed } = useAppTheme();

  return (
    <View style={[themed($container), style]}>
      {icon && <View style={themed($iconContainer)}>{icon}</View>}

      {(title || titleTx) && (
        <Text preset="subheading" text={title} tx={titleTx} txOptions={titleTxOptions} style={themed($title)} />
      )}

      {(message || messageTx) && (
        <Text text={message} tx={messageTx} txOptions={messageTxOptions} style={themed($message)} size="sm" />
      )}

      {onAction && (actionText || actionTx) && (
        <Button
          text={actionText}
          tx={actionTx}
          txOptions={actionTxOptions}
          onPress={onAction}
          preset="filled"
          style={themed($actionButton)}
          {...buttonProps}
        />
      )}
    </View>
  );
});

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
  paddingHorizontal: spacing.lg,
});

const $iconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
});

const $title: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  textAlign: "center",
  marginBottom: spacing.sm,
});

const $message: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  textAlign: "center",
  marginBottom: spacing.lg,
});

const $actionButton: ThemedStyle<ViewStyle> = () => ({
  minWidth: 120,
});
