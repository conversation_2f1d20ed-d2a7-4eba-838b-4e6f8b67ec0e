import React, { useCallback } from "react";
import { type TextStyle, View, type ViewStyle } from "react-native";
import { HeroImage } from "@/components/heroes/HeroImage";
import { HeroName } from "@/components/heroes/HeroName";
import { Text } from "@/components/ui/Text";
import { translate } from "@/i18n/translate";
import type { MatchHistory } from "@/services/api/types/match_history";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";
import { formatMatchDuration, formatRelativeTime, parseMatchMode } from "@/utils/format";
import { isMatchWon } from "@/utils/validation";
import { BaseCard } from "../cards/BaseCard";
import { StatColumn } from "../stats/StatColumn";
import { StatsRow } from "../stats/StatsRow";

export interface OptimizedMatchItemProps {
  match: MatchHistory;
  onPress: (match: MatchHistory) => void;
  /**
   * Whether this match item is selected/highlighted
   */
  isSelected?: boolean;
  /**
   * Whether to show detailed stats
   */
  showDetailedStats?: boolean;
}

/**
 * Optimized match item component with React.memo and performance optimizations
 * Uses shared components and avoids inline functions for better performance
 */
export const OptimizedMatchItem = React.memo<OptimizedMatchItemProps>(function OptimizedMatchItem(props) {
  const { match, onPress, isSelected = false, showDetailedStats = true } = props;
  const { themed, theme } = useAppTheme();

  // Memoize the press handler to avoid recreating on each render
  const handlePress = useCallback(() => {
    onPress(match);
  }, [match, onPress]);

  // Memoize computed values
  const matchWon = React.useMemo(() => isMatchWon(match), [match]);
  const resultColor = React.useMemo(
    () => (matchWon ? theme.colors.palette.success500 : theme.colors.palette.failure500),
    [matchWon, theme.colors.palette.success500, theme.colors.palette.failure500],
  );
  const resultText = React.useMemo(
    () => (matchWon ? translate("common:victory") : translate("common:defeat")),
    [matchWon],
  );
  const formattedNetWorth = React.useMemo(() => `${(match.net_worth / 1000).toFixed(0)}k`, [match.net_worth]);
  const formattedDuration = React.useMemo(() => formatMatchDuration(match.match_duration_s), [match.match_duration_s]);
  const formattedTime = React.useMemo(() => formatRelativeTime(match.start_time), [match.start_time]);
  const matchMode = React.useMemo(() => parseMatchMode(match.match_mode), [match.match_mode]);

  const cardStyle = [isSelected && themed($selectedCard)];

  return (
    <BaseCard style={cardStyle} variant="elevated" pressable onPress={handlePress} activeOpacity={0.7}>
      <View style={themed($headerRow)}>
        <View style={themed($matchHero)}>
          <HeroImage heroId={match.hero_id} size={40} />
          <View style={themed($matchInfo)}>
            <Text numberOfLines={1} style={themed($heroName)}>
              <HeroName heroId={match.hero_id} />
            </Text>
            <Text numberOfLines={1} style={themed($timeText)} size="xxs">
              {matchMode}
            </Text>
          </View>
        </View>

        <View style={themed($matchStats)}>
          <Text style={[themed($matchResult), { color: resultColor }]} size="sm">
            {resultText}
          </Text>
          <Text style={themed($timeText)} size="xxs">
            {formattedDuration}
          </Text>
          <Text style={themed($timeText)} size="xxs">
            {formattedTime}
          </Text>
        </View>
      </View>

      {showDetailedStats && (
        <StatsRow gap="sm">
          <StatColumn
            labelTx="matchItem:kda"
            value={`${match.player_kills}/${match.player_deaths}/${match.player_assists}`}
            valueSize="xs"
          />
          <StatColumn labelTx="matchItem:lastHits" value={match.last_hits} valueSize="xs" />
          <StatColumn labelTx="matchItem:denies" value={match.denies} valueSize="xs" />
          <StatColumn labelTx="matchItem:level" value={match.hero_level} valueSize="xs" />
          <StatColumn labelTx="matchItem:netWorth" value={formattedNetWorth} valueSize="xs" />
        </StatsRow>
      )}
    </BaseCard>
  );
});

// Styled components
const $selectedCard: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderWidth: 2,
  borderColor: colors.tint,
});

const $headerRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.sm,
});

const $matchHero: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
});

const $matchInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  marginLeft: spacing.sm,
});

const $heroName: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.semiBold,
});

const $matchStats: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-end",
});

const $matchResult: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.bold,
});

const $timeText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
});
