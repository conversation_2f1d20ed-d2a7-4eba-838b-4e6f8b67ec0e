import React from "react";
import { View, type ViewStyle } from "react-native";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface StatsGridProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * Number of columns in the grid
   */
  columns?: number;
  /**
   * Children components to render in the grid
   */
  children: React.ReactNode;
  /**
   * Gap between grid items
   */
  gap?: "xs" | "sm" | "md" | "lg";
}

/**
 * A flexible grid layout component for displaying stats or cards
 * Automatically wraps items based on the number of columns
 */
export const StatsGrid = React.memo<StatsGridProps>(function StatsGrid(props) {
  const { style, columns = 2, children, gap = "sm" } = props;
  const { themed, theme } = useAppTheme();

  const gridStyle = [themed($container), { gap: theme.spacing[gap] }, style];

  // Convert children to array and chunk them based on columns
  const childrenArray = React.Children.toArray(children);
  const rows: React.ReactNode[][] = [];

  for (let i = 0; i < childrenArray.length; i += columns) {
    rows.push(childrenArray.slice(i, i + columns));
  }

  return (
    <View style={gridStyle}>
      {rows.map((row, rowIndex) => (
        <View key={rowIndex} style={themed($row)}>
          {row.map((child, childIndex) => (
            <View key={childIndex} style={[themed($item), { flex: 1 / columns }]}>
              {child}
            </View>
          ))}
          {/* Fill remaining space if row is not complete */}
          {row.length < columns &&
            Array.from({ length: columns - row.length }).map((_, index) => (
              <View key={`empty-${index}`} style={[themed($item), { flex: 1 / columns }]} />
            ))}
        </View>
      ))}
    </View>
  );
});

const $container: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "column",
});

const $row: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.sm,
});

const $item: ThemedStyle<ViewStyle> = () => ({
  flexGrow: 1,
});
