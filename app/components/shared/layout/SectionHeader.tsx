import React from "react";
import { View, type ViewStyle } from "react-native";
import { Text, type TextProps } from "@/components/ui/Text";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface SectionHeaderProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * The title text to display
   */
  title?: string;
  /**
   * Title text which is looked up via i18n.
   */
  titleTx?: TextProps["tx"];
  /**
   * Optional title options to pass to i18n.
   */
  titleTxOptions?: TextProps["txOptions"];
  /**
   * Optional subtitle text
   */
  subtitle?: string;
  /**
   * Subtitle text which is looked up via i18n.
   */
  subtitleTx?: TextProps["tx"];
  /**
   * Optional subtitle options to pass to i18n.
   */
  subtitleTxOptions?: TextProps["txOptions"];
  /**
   * Optional action component to display on the right
   */
  rightAction?: React.ReactNode;
  /**
   * Optional icon to display next to the title
   */
  leftIcon?: React.ReactNode;
}

/**
 * A reusable section header component with title, subtitle, and optional actions
 */
export const SectionHeader = React.memo<SectionHeaderProps>(function SectionHeader(props) {
  const { style, title, titleTx, titleTxOptions, subtitle, subtitleTx, subtitleTxOptions, rightAction, leftIcon } =
    props;

  const { themed } = useAppTheme();

  return (
    <View style={[themed($container), style]}>
      <View style={themed($leftContent)}>
        {leftIcon && <View style={themed($iconContainer)}>{leftIcon}</View>}
        <View style={themed($textContainer)}>
          <Text preset="subheading" text={title} tx={titleTx} txOptions={titleTxOptions} style={themed($title)} />
          {(subtitle || subtitleTx) && (
            <Text size="xs" text={subtitle} tx={subtitleTx} txOptions={subtitleTxOptions} style={themed($subtitle)} />
          )}
        </View>
      </View>

      {rightAction && <View style={themed($rightAction)}>{rightAction}</View>}
    </View>
  );
});

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: spacing.sm,
});

const $leftContent: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
});

const $iconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginRight: spacing.sm,
});

const $textContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
});

const $title: ThemedStyle<ViewStyle> = () => ({
  // Title styles are handled by the Text component preset
});

const $subtitle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim,
});

const $rightAction: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-end",
});
