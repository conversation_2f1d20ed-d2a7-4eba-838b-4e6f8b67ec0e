/**
 * API response utilities
 */

import type { ApiResponse } from "apisauce";

/**
 * Transforms API response data using a provided transformer function
 * @param response The API response
 * @param transformer Function to transform the data
 * @returns Transformed API response
 */
export function transformResponse<T, U>(response: ApiResponse<T>, transformer: (data: T) => U): ApiResponse<U> {
  if (!response.ok || !response.data) {
    return response as ApiResponse<U>;
  }

  return {
    ...response,
    data: transformer(response.data),
  };
}

/**
 * Filters array data in API response based on predicate
 * @param response The API response containing array data
 * @param predicate Function to filter items
 * @returns Filtered API response
 */
export function filterResponseData<T>(response: ApiResponse<T[]>, predicate: (item: T) => boolean): ApiResponse<T[]> {
  return transformResponse(response, (data) => data.filter(predicate));
}

/**
 * Sorts array data in API response
 * @param response The API response containing array data
 * @param compareFn Function to compare items for sorting
 * @returns Sorted API response
 */
export function sortResponseData<T>(response: ApiResponse<T[]>, compareFn: (a: T, b: T) => number): ApiResponse<T[]> {
  return transformResponse(response, (data) => [...data].sort(compareFn));
}

/**
 * Paginates array data in API response
 * @param response The API response containing array data
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @returns Paginated API response with metadata
 */
export function paginateResponseData<T>(
  response: ApiResponse<T[]>,
  page: number,
  pageSize: number,
): ApiResponse<{
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {
  return transformResponse(response, (data) => {
    const total = data.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  });
}

/**
 * Combines multiple API responses into a single response
 * @param responses Array of API responses
 * @param combiner Function to combine the data
 * @returns Combined API response
 */
export function combineResponses<T, U>(responses: ApiResponse<T>[], combiner: (data: T[]) => U): ApiResponse<U> {
  // Check if all responses are successful
  const failedResponse = responses.find((response) => !response.ok);
  if (failedResponse) {
    return failedResponse as ApiResponse<U>;
  }

  // Extract data from all responses
  const allData = responses.map((response) => response.data).filter(Boolean) as T[];

  // Use the first response as the base and combine data
  const baseResponse = responses[0];
  return {
    ...baseResponse,
    data: combiner(allData),
  };
}

/**
 * Caches API response data with expiration
 * @param key Cache key
 * @param response API response to cache
 * @param ttlMs Time to live in milliseconds
 */
export function cacheResponse<T>(key: string, response: ApiResponse<T>, ttlMs: number): void {
  if (!response.ok || !response.data) {
    return;
  }

  const cacheItem = {
    data: response.data,
    timestamp: Date.now(),
    ttl: ttlMs,
  };

  try {
    localStorage.setItem(`api_cache_${key}`, JSON.stringify(cacheItem));
  } catch (error) {
    console.warn("Failed to cache API response:", error);
  }
}

/**
 * Retrieves cached API response data
 * @param key Cache key
 * @returns Cached data or null if not found/expired
 */
export function getCachedResponse<T>(key: string): T | null {
  try {
    const cached = localStorage.getItem(`api_cache_${key}`);
    if (!cached) {
      return null;
    }

    const cacheItem = JSON.parse(cached);
    const now = Date.now();

    if (now - cacheItem.timestamp > cacheItem.ttl) {
      localStorage.removeItem(`api_cache_${key}`);
      return null;
    }

    return cacheItem.data;
  } catch (error) {
    console.warn("Failed to retrieve cached API response:", error);
    return null;
  }
}

/**
 * Clears expired cache entries
 */
export function clearExpiredCache(): void {
  try {
    const keys = Object.keys(localStorage).filter((key) => key.startsWith("api_cache_"));
    const now = Date.now();

    keys.forEach((key) => {
      try {
        const cached = localStorage.getItem(key);
        if (cached) {
          const cacheItem = JSON.parse(cached);
          if (now - cacheItem.timestamp > cacheItem.ttl) {
            localStorage.removeItem(key);
          }
        }
      } catch {
        // Remove invalid cache entries
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn("Failed to clear expired cache:", error);
  }
}

/**
 * Creates a mock API response for testing
 * @param data The mock data
 * @param ok Whether the response is successful
 * @param status HTTP status code
 * @returns Mock API response
 */
export function createMockResponse<T>(data: T, ok: boolean = true, status: number = 200): ApiResponse<T> {
  return {
    ok,
    status,
    data,
    headers: {},
    config: {},
    duration: 100,
    problem: ok ? null : "CLIENT_ERROR",
    originalError: null,
  };
}
