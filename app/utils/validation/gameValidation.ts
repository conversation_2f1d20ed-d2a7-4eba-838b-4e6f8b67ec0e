/**
 * Game-specific validation utilities
 */

import type { MatchHistory } from "@/services/api/types/match_history";
import { isValidNumber, isValidTimestamp } from "./typeGuards";

/**
 * Validates if a Steam ID is in the correct format
 * @param steamId The Steam ID to validate
 * @returns true if valid Steam ID format
 */
export function isValidSteamIdFormat(steamId: string): boolean {
  // Steam64 ID should be a 17-digit number starting with 7656119
  const steamIdRegex = /^7656119\d{10}$/;
  return steamIdRegex.test(steamId);
}

/**
 * Converts a Steam64 ID to SteamID3 format and validates it
 * @param steam64 The Steam64 ID string
 * @returns The SteamID3 format number or null if invalid
 */
export function convertAndValidateSteamID3(steam64: string): number | null {
  if (!isValidSteamIdFormat(steam64)) {
    return null;
  }

  try {
    const steamId3 = Number(BigInt(steam64) - BigInt("7****************"));
    return isValidNumber(steamId3) ? steamId3 : null;
  } catch {
    return null;
  }
}

/**
 * Validates if a match history object has all required fields
 * @param match The match history object to validate
 * @returns true if the match object is valid
 */
export function isValidMatchHistory(match: unknown): match is MatchHistory {
  if (typeof match !== "object" || match === null) {
    return false;
  }

  const m = match as Record<string, unknown>;

  return (
    isValidNumber(m.account_id) &&
    isValidNumber(m.match_id) &&
    isValidNumber(m.hero_id) &&
    isValidNumber(m.hero_level) &&
    isValidTimestamp(m.start_time) &&
    isValidNumber(m.game_mode) &&
    isValidNumber(m.match_mode) &&
    isValidNumber(m.player_team) &&
    isValidNumber(m.player_kills) &&
    isValidNumber(m.player_deaths) &&
    isValidNumber(m.player_assists) &&
    isValidNumber(m.denies) &&
    isValidNumber(m.net_worth) &&
    isValidNumber(m.last_hits) &&
    isValidNumber(m.match_duration_s) &&
    isValidNumber(m.match_result)
  );
}

/**
 * Determines if a match was won by the player
 * @param match MatchHistory object
 * @returns true if the player won, false if they lost, null if invalid
 */
export function isMatchWon(match: MatchHistory): boolean | null {
  if (!isValidMatchHistory(match)) {
    return null;
  }
  return match.match_result === match.player_team;
}

/**
 * Validates if a hero ID is within expected range
 * @param heroId The hero ID to validate
 * @returns true if valid hero ID
 */
export function isValidHeroId(heroId: unknown): heroId is number {
  return isValidNumber(heroId) && heroId > 0 && heroId <= 100; // Assuming max 100 heroes
}

/**
 * Validates if a match mode is recognized
 * @param matchMode The match mode to validate
 * @returns true if valid match mode
 */
export function isValidMatchMode(matchMode: unknown): matchMode is number {
  const validModes = [1, 2, 3, 4, 5, 6, 7]; // Based on parseMatchMode function
  return isValidNumber(matchMode) && validModes.includes(matchMode);
}

/**
 * Validates if a badge number is in correct format
 * @param badgeNumber The badge number to validate
 * @returns true if valid badge format
 */
export function isValidBadgeNumber(badgeNumber: unknown): badgeNumber is number {
  if (!isValidNumber(badgeNumber) || badgeNumber <= 0) {
    return false;
  }

  const badgeStr = badgeNumber.toString();

  // Badge should have at least 2 digits (tier + subtier)
  if (badgeStr.length < 2) {
    return false;
  }

  const subtier = Number.parseInt(badgeStr.slice(-1), 10);
  const tier = Number.parseInt(badgeStr.slice(0, -1), 10);

  // Validate tier and subtier ranges
  return (
    !Number.isNaN(tier) &&
    !Number.isNaN(subtier) &&
    tier >= 1 &&
    tier <= 11 && // Assuming 11 tiers max
    subtier >= 1 &&
    subtier <= 6 // Assuming 6 subtiers max
  );
}

/**
 * Validates if KDA values are reasonable
 * @param kills Number of kills
 * @param deaths Number of deaths
 * @param assists Number of assists
 * @returns true if KDA values are valid
 */
export function isValidKDA(kills: unknown, deaths: unknown, assists: unknown): boolean {
  return (
    isValidNumber(kills) &&
    kills >= 0 &&
    isValidNumber(deaths) &&
    deaths >= 0 &&
    isValidNumber(assists) &&
    assists >= 0 &&
    kills <= 100 && // Reasonable upper bounds
    deaths <= 100 &&
    assists <= 200
  );
}

/**
 * Validates if a time range is reasonable for filtering matches
 * @param startTime Start timestamp
 * @param endTime End timestamp
 * @returns true if time range is valid
 */
export function isValidTimeRange(startTime: unknown, endTime: unknown): boolean {
  if (!isValidTimestamp(startTime) || !isValidTimestamp(endTime)) {
    return false;
  }

  // End time should be after start time
  // Time range shouldn't be more than 1 year
  const oneYear = 365 * 24 * 60 * 60; // seconds in a year

  return endTime > startTime && endTime - startTime <= oneYear;
}
