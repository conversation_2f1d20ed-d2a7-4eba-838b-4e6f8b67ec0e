import React from "react";
import { View, type ViewStyle } from "react-native";
import type { StyleProp } from "react-native/Libraries/StyleSheet/StyleSheet";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";

export interface StatsRowProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle;
  /**
   * Children components to render in the row
   */
  children: React.ReactNode;
  /**
   * How to distribute space between items
   */
  distribution?: "space-between" | "space-around" | "space-evenly" | "flex-start" | "flex-end" | "center";
  /**
   * Gap between items
   */
  gap?: "xs" | "sm" | "md" | "lg";
  /**
   * Whether to wrap items to next line
   */
  wrap?: boolean;
}

/**
 * A flexible row component for displaying stats in a horizontal layout
 */
export const StatsRow = React.memo<StatsRowProps>(function StatsRow(props) {
  const { style, children, distribution = "space-between", gap, wrap = false } = props;
  const { themed, theme } = useAppTheme();

  const rowStyle: StyleProp<ViewStyle> = [
    themed($container),
    { justifyContent: distribution },
    gap && { gap: theme.spacing[gap] },
    wrap && { flexWrap: "wrap" },
    style,
  ];

  return <View style={rowStyle}>{children}</View>;
});

const $container: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  width: "100%",
});
