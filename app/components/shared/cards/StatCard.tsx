import React from "react";
import { Dimensions, type DimensionValue, type TextStyle, View, type ViewStyle } from "react-native";
import { Text } from "@/components/ui/Text";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";
import { BaseCard, type BaseCardProps } from "./BaseCard";

const { width: screenWidth } = Dimensions.get("window");

export interface StatCardProps extends Omit<BaseCardProps, "children"> {
  /**
   * The title text or component to display
   */
  title: string | React.ReactNode;
  /**
   * The main value to display
   */
  value: string | number | React.ReactNode;
  /**
   * Optional color override for the value text
   */
  valueColor?: string;
  /**
   * Optional subtitle text
   */
  subtitle?: string;
  /**
   * Width of the card
   */
  width?: DimensionValue;
  /**
   * Font size for the value
   */
  valueSize?: number;
  /**
   * Optional icon to display next to the title
   */
  titleIcon?: React.ReactNode;
}

/**
 * A reusable stat card component for displaying key-value pairs
 * with consistent styling and optional subtitle
 */
export const StatCard = React.memo<StatCardProps>(function StatCard(props) {
  const {
    title,
    value,
    subtitle,
    valueColor,
    width,
    valueSize = 24,
    titleIcon,
    style,
    variant = "elevated",
    ...baseCardProps
  } = props;

  const { themed } = useAppTheme();

  const cardStyle = [{ width: width ?? screenWidth / 2 - 32 }, style];

  return (
    <BaseCard style={cardStyle} variant={variant} {...baseCardProps}>
      <View style={themed($statCardHeader)}>
        <View style={themed($titleContainer)}>
          {titleIcon}
          {React.isValidElement(title) ? (
            title
          ) : (
            <Text style={themed($statTitle)} size="xs">
              {title}
            </Text>
          )}
        </View>
      </View>

      {React.isValidElement(value) ? (
        value
      ) : (
        <Text style={[themed($statValue), valueColor && { color: valueColor }, { fontSize: valueSize }]}>{value}</Text>
      )}

      {subtitle && (
        <Text size="xxs" style={themed($statSubtitleText)}>
          {subtitle}
        </Text>
      )}
    </BaseCard>
  );
});

const $statCardHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.xxs,
});

const $titleContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xxs,
});

const $statTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
});

const $statValue: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: 4,
});

const $statSubtitleText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  color: colors.textDim,
});
